"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Shield, Users, Zap, Star, Gift, CheckCircle } from "lucide-react";
import <PERSON> from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";

// Changelog data structure
const changelogEntries = [
  {
    version: "1.1",
    title: "Quick Improvements",
    date: "July 15, 2025",
    isLatest: true,
    summary: "Quick fixes and improvements based on initial user feedback",
    changes: [
      {
        type: "improvement",
        category: "User Experience",
        icon: Users,
        items: [
          "Added account settings page for better profile management",
          "Enhanced user data handling and privacy protection",
          "Added account deletion option for user control"
        ]
      },
      {
        type: "security",
        category: "Security & Stability",
        icon: Shield,
        items: [
          "Enhanced database security with better user data protection",
          "Improved authentication system reliability",
          "Fixed various bugs and improved error handling",
          "Better data validation and security measures"
        ]
      },
      {
        type: "performance",
        category: "Performance & Bug Fixes",
        icon: Zap,
        items: [
          "Fixed portfolio slug generation for cleaner URLs",
          "Improved loading times and responsiveness",
          "Better error messages and user feedback",
          "Code cleanup and optimization improvements"
        ]
      }
    ]
  },
  {
    version: "1.0",
    title: "Platform Launch",
    date: "July 14, 2025",
    isLatest: false,
    summary: "Initial release with complete portfolio building platform",
    changes: [
      {
        type: "feature",
        category: "Export Your Portfolio",
        icon: Gift,
        items: [
          "Download your portfolio as a complete website",
          "Host anywhere - no dependencies required",
          "Perfect pixel-by-pixel copy of your live site",
          "Mobile-responsive exported sites"
        ]
      },
      {
        type: "feature",
        category: "Multiple Themes",
        icon: Sparkles,
        items: [
          "Modern theme for developers and tech professionals",
          "Creative Minimalist theme for designers",
          "Consistent editing experience across themes"
        ]
      },
      {
        type: "feature",
        category: "Core Features",
        icon: Zap,
        items: [
          "Live portfolio editor with real-time preview",
          "Google authentication for easy sign-up",
          "Image upload and optimization",
          "Public portfolio publishing with custom URLs"
        ]
      }
    ]
  }
];

const comingSoonFeatures = [
  {
    title: "Premium Themes",
    description: "Advanced theme designs with premium customization options",
    icon: Star
  },
  {
    title: "Custom Domains",
    description: "Connect your own domain to your portfolio",
    icon: Zap
  }
];

export default function ChangelogPage() {
  return (
    <div className="min-h-screen bg-backgroundPrimary text-textPrimary">
      {/* Header */}
      <header className="sticky top-0 z-20 bg-backgroundPrimary/80 backdrop-blur-lg border-b border-borderPrimary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            <div className="flex items-center gap-4">
              <Link href="/" className="flex items-center gap-2 text-lg font-bold gradient-text">
                <Sparkles className="w-5 h-5 text-brandPrimary" />
                Profolify
              </Link>
            </div>
            <Link href="/">
              <Button variant="ghost" className="flex items-center gap-2">
                <ArrowLeft className="w-4 h-4" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 text-center bg-backgroundSecondary border-b border-borderPrimary">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <h1 className="text-4xl sm:text-5xl font-bold mb-4">Changelog</h1>
          <p className="text-lg text-textSecondary max-w-2xl mx-auto">
            Stay updated with the latest features, improvements, and bug fixes.
          </p>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="max-w-4xl mx-auto">
          <div className="space-y-16">
            {changelogEntries.map((entry, entryIndex) => (
              <div key={entry.version} className="relative">
                {/* Timeline Line */}
                {entryIndex < changelogEntries.length - 1 && (
                  <div className="absolute left-5 top-12 bottom-0 w-0.5 bg-borderPrimary"></div>
                )}

                {/* Entry Header */}
                <div className="flex items-center gap-4 mb-8">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                    entry.isLatest ? 'bg-brandPrimary text-white' : 'bg-backgroundSecondary border border-borderPrimary'
                  }`}>
                    {entry.isLatest ? <Sparkles className="w-5 h-5" /> : <CheckCircle className="w-5 h-5" />}
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold">{entry.title} - v{entry.version}</h2>
                    <p className="text-sm text-textSecondary">{entry.date}</p>
                  </div>
                </div>

                {/* Changes */}
                <div className="ml-14 space-y-8">
                  {entry.changes.map((changeGroup, groupIndex) => {
                    const IconComponent = changeGroup.icon;
                    const typeClasses = {
                      feature: 'bg-brandSecondary/10 text-brandSecondary',
                      improvement: 'bg-brandPrimary/10 text-brandPrimary',
                      security: 'bg-brandAccent/10 text-brandAccent',
                      performance: 'bg-yellow-500/10 text-yellow-500',
                    }[changeGroup.type] || 'bg-gray-500/10 text-gray-500';

                    return (
                      <div key={groupIndex} className="p-6 rounded-lg bg-backgroundSecondary border border-borderPrimary">
                        <div className="flex items-center gap-3 mb-4">
                          <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${typeClasses}`}>
                            <IconComponent className="w-4 h-4" />
                          </div>
                          <h3 className="font-semibold text-lg">{changeGroup.category}</h3>
                        </div>
                        <ul className="space-y-2 pl-4">
                          {changeGroup.items.map((item, itemIndex) => (
                            <li key={itemIndex} className="flex items-start gap-3 text-textSecondary">
                              <CheckCircle className="w-4 h-4 text-brandSecondary mt-1 flex-shrink-0" />
                              <span>{item}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>

          {/* Coming Soon */}
          <div className="mt-20 pt-16 border-t border-borderPrimary">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">What&apos;s Next?</h2>
              <p className="text-textSecondary max-w-xl mx-auto">
                We are always working on new features. Here&apos;s a sneak peek at what&apos;s coming soon.
              </p>
            </div>
            <div className="grid md:grid-cols-2 gap-8 max-w-3xl mx-auto">
              {comingSoonFeatures.map((feature, index) => {
                const IconComponent = feature.icon;
                return (
                  <div key={index} className="p-6 rounded-lg bg-backgroundSecondary border border-borderPrimary text-center">
                    <div className="w-12 h-12 bg-brandPrimary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-6 h-6 text-brandPrimary" />
                    </div>
                    <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                    <p className="text-sm text-textSecondary">{feature.description}</p>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
