# Theme Development Guide

Profolify’s theme system is modular, extensible, and designed for both rapid development and pixel-perfect export. Version 2.0 introduces profession-specific and organization-specific theme support.

---

## 1. Theme System Overview

- **Themes** are self-contained packages of React components and CSS.
- **Dual-Mode Pattern:** Each theme component supports both editing (in the dashboard) and static (public/exported) modes.
- **CSS Architecture:** Modular in development, compiled for production/export.
- **🆕 Profession-Specific Support:** Themes can target specific professions or support multiple profession types.
- **🆕 Type Safety:** Full TypeScript support with profession-specific data interfaces.

---

## 2. Profession-Specific Theme Architecture

### 2.1 Theme Categories & Profession Types

Themes can now target specific professions or support multiple profession types:

```typescript
// Theme configuration
export interface ThemeConfig {
  id: string;
  name: string;
  themeCategory: ThemeCategory;
  professionTypes: ProfessionType[];
  organizationTypes?: OrganizationType[];
}

// Example: Multi-profession theme
const modernTheme: ThemeConfig = {
  id: 'modern',
  name: 'Modern',
  themeCategory: 'general',
  professionTypes: ['general', 'it-professional', 'business-analyst'],
};

// Example: Profession-specific theme
const medicalTheme: ThemeConfig = {
  id: 'medical-professional',
  name: 'Medical Professional',
  themeCategory: 'healthcare',
  professionTypes: ['doctor', 'nurse'],
};
```

### 2.2 Data Interface Handling

Theme components must handle different portfolio data types safely:

```typescript
import { AnyPortfolioData, isGeneralPortfolio, isITPortfolio } from '@/lib/types';

export default function ProjectsSection({ data, isEditing }: SectionProps) {
  // Only show projects for portfolio types that support them
  if (!isGeneralPortfolio(data) && !isITPortfolio(data)) {
    return null;
  }

  const projects = data.projects || [];
  // ... component logic
}
```

### 2.3 Conditional Field Rendering

```typescript
// Example: Qualification fields only for certain professions
{(isGeneralPortfolio(data) || isITPortfolio(data)) && data.qualification1 && (
  <div className="qualification">
    <EditableText
      value={data.qualification1}
      onChange={(value) => handleUpdate('qualification1', value)}
      placeholder="Enter your qualification..."
      isEditing={isEditing}
    />
  </div>
)}
```

---

## 3. Theme Directory Structure

themes/ my-awesome-theme/ components/ Hero.tsx About.tsx ... hero.css about.css my-awesome-theme-modular.css my-awesome-theme-compiled.css (auto-generated)


---

## 4. Step-by-Step: Creating a New Theme

### 1. Create the Theme Directory

- Inside `themes/`, create a new folder:
  `themes/my-awesome-theme/`

### 2. Add Components

- In `components/`, create React components for each section (e.g., `Hero.tsx`, `About.tsx`).
- Each component should accept:
  - `isEditing: boolean` (toggles between editor and public mode)
  - `serverData?: AnyPortfolioData` (for static rendering - updated for v2.0)
  - Use `EditableText` and other editor helpers for inline editing.

### 2.1 🆕 Handle Different Portfolio Types

```typescript
import { SectionProps, AnyPortfolioData, isGeneralPortfolio, isMedicalPortfolio } from '@/lib/types';

export default function AboutSection({ data, isEditing }: SectionProps) {
  // Type-safe access to profession-specific fields
  if (isMedicalPortfolio(data)) {
    // Access medical-specific fields
    const specializations = data.specializations;
    const certifications = data.certifications;
  }

  // Conditional rendering based on portfolio type
  return (
    <section>
      {/* Common fields available to all portfolio types */}
      <EditableText value={data.about} ... />

      {/* Profession-specific fields */}
      {isMedicalPortfolio(data) && (
        <div className="specializations">
          {data.specializations.map(spec => (
            <span key={spec}>{spec}</span>
          ))}
        </div>
      )}
    </section>
  );
}
```

### 3. Add CSS

- Create a CSS file for each component (e.g., `hero.css`).
- In the root, create `my-awesome-theme-modular.css`:
  ``` css

  @import './components/hero.css';
  @import './components/about.css';
  /* ... */

  ```

### 4. 🆕 Register the Theme with Profession Support

- Open `themes/theme-registry.ts`.
- Import your main theme component.
- Add a new entry to the THEME_REGISTRY array with profession targeting:

```typescript
{
  id: 'my-awesome-theme',
  name: 'My Awesome Theme',
  description: 'A bold, modern theme for creative professionals.',
  component: MyAwesomeTheme,
  preview: '/thumbnails/my-awesome-theme.jpg',
  modularCss: '/themes/my-awesome-theme/my-awesome-theme-modular.css',
  compiledCss: '/themes/my-awesome-theme/my-awesome-theme-compiled.css',
  isPremium: false, // or true for paid themes
  // 🆕 New fields for profession targeting
  themeCategory: 'general', // or 'healthcare', 'technology', etc.
  professionTypes: ['general', 'it-professional'], // Array of supported professions
  organizationTypes: ['general'], // Optional: for organization themes
}
```

### 4.1 Theme Category Examples

```typescript
// General theme (supports multiple professions)
{
  themeCategory: 'general',
  professionTypes: ['general', 'it-professional', 'business-analyst'],
}

// Healthcare-specific theme
{
  themeCategory: 'healthcare',
  professionTypes: ['doctor', 'nurse'],
}

// Creative professionals theme
{
  themeCategory: 'creative',
  professionTypes: ['photographer', 'designer'],
}

// Organization theme
{
  themeCategory: 'organization',
  professionTypes: [], // Not applicable for organizations
  organizationTypes: ['hospital', 'tech-company'],
}
```

### 5. Compile and Preview

- Run:
``` bash npm run sync-themes ```

The dev server will hot-reload your theme.

---

## 5. Creating Profession-Specific Themes

### 5.1 Medical Professional Theme Example

```typescript
// themes/medical-professional/components/MedicalAbout.tsx
import { SectionProps, isMedicalPortfolio } from '@/lib/types';

export default function MedicalAbout({ data, isEditing }: SectionProps) {
  if (!isMedicalPortfolio(data)) {
    return null; // This theme only works with medical portfolios
  }

  return (
    <section className="medical-about">
      <div className="medical-info">
        <h2>Medical Background</h2>

        {/* Medical License */}
        {data.medicalLicense && (
          <div className="license">
            <strong>License:</strong> {data.medicalLicense}
          </div>
        )}

        {/* Specializations */}
        <div className="specializations">
          <h3>Specializations</h3>
          {data.specializations.map(spec => (
            <span key={spec} className="specialization-badge">{spec}</span>
          ))}
        </div>

        {/* Certifications */}
        <div className="certifications">
          <h3>Certifications</h3>
          {data.certifications.map(cert => (
            <div key={cert} className="certification">{cert}</div>
          ))}
        </div>
      </div>
    </section>
  );
}
```

### 5.2 IT Professional Theme Example

```typescript
// themes/tech-professional/components/TechProjects.tsx
import { SectionProps, isITPortfolio } from '@/lib/types';

export default function TechProjects({ data, isEditing }: SectionProps) {
  if (!isITPortfolio(data)) {
    return null;
  }

  return (
    <section className="tech-projects">
      {data.projects.map(project => (
        <div key={project.id} className="tech-project-card">
          <h3>{project.title}</h3>
          <p>{project.description}</p>

          {/* IT-specific fields */}
          {project.technologies && (
            <div className="technologies">
              {project.technologies.map(tech => (
                <span key={tech} className="tech-badge">{tech}</span>
              ))}
            </div>
          )}

          {project.githubUrl && (
            <a href={project.githubUrl} className="github-link">
              View on GitHub
            </a>
          )}
        </div>
      ))}
    </section>
  );
}
```

---

## 6. Dual-Mode Component Pattern

Every theme component must support both editing and static modes:

```tsx
export default function Hero({ isEditing, serverData }) {
  const data = isEditing ? useEditorContext().state.formData : serverData;
  return (
    <section>
      <EditableText isEditing={isEditing} initialValue={data.title} />
      {isEditing && <ImageUploadButton />}
      {/* ... */}
    </section>
  );
}
```

- `isEditing = true`: Show inline editing, upload buttons, etc.
- `isEditing = false`: Render static HTML for public/exported site.

### 5. CSS Best Practices

- **Editor Navbar Override**: To ensure your theme's navbar renders correctly inside the portfolio editor canvas (preventing it from becoming `fixed` and obscuring the UI), add the generic `theme-navbar` class to your main navbar element alongside your theme-specific class. The editor uses this class to apply necessary style overrides.

  ```tsx
  // Example from ModernNavbar.tsx
  <header className="theme-modern-navbar theme-navbar">
    {/* ... */}
  </header>
  ```

- Use CSS variables for colors, fonts, and spacing to enable future customization.
- Keep component CSS modular and minimal.
- Use Tailwind CSS utility classes where possible for consistency.

### 6. Advanced: Dynamic Customization

- To support user-driven customization (colors, fonts), use CSS variables:
```css
:root {
  --theme-primary-color: #007bff;
  --theme-font-family: 'Inter', sans-serif;
}
```

```css
.hero-title {
  color: var(--theme-primary-color);
  font-family: var(--theme-font-family);
}
```

- Inject a `<style>` block in the page `<head>` to override variables based on user settings.


### 7. Export Compatibility

- Avoid using browser-only APIs or dynamic imports in theme components.
- All assets (images, fonts) should be web-accessible and referenced by URL.

### 8. How to Publish a Theme

- Add a preview image to /public/thumbnails/.
- Update the theme registry.
- Test in both editor and public/export modes.

### 9. Example: Minimal Theme Component

```tsx
import './hero.css';
import EditableText from '@/components/custom-ui/EditableText';

export default function Hero({ isEditing, serverData }) {
  const data = isEditing ? useEditorContext().state.formData : serverData;
  return (
    <section className="hero">
      <EditableText isEditing={isEditing} initialValue={data.title} />
      <p>{data.subtitle}</p>
    </section>
  );
}
```

### 10. Validation System Integration

**Real-time Portfolio Validation (v1.3.0+)**

All theme components must integrate with Profolify's validation system to provide real-time error feedback to users.

#### Required Integration Steps:

1. **Import Validation Context:**
```tsx
import { useEditorContext } from '@/contexts/EditorContext';
import { getFieldErrors } from '@/lib/portfolio-validation';
```

2. **Add Validation Props to EditableText:**
```tsx
const { state } = useEditorContext();
const getFieldError = (fieldName: string) => {
  if (!state.validationResult) return undefined;
  const errors = getFieldErrors(state.validationResult, fieldName);
  return errors.length > 0 ? errors[0].message : undefined;
};

// Example usage in component
<EditableText
  isEditing={isEditing}
  initialValue={data.bio}
  placeholder="Tell visitors about yourself..."
  hasError={!!getFieldError('bio')}
  errorMessage={getFieldError('bio')}
  onSave={(value) => handleUpdate('bio', value)}
/>
```

3. **Required Fields by Section:**
- **Hero**: `userName`, `profession`
- **About**: `bio`, at least one of `qualification1` or `qualification2`
- **Contact**: `email` (phone is optional)
- **Skills**: minimum 3 skills required
- **Experience**: minimum 1 experience entry required
- **Projects**: minimum 1 project required

#### Qualification Fields Implementation:

Both Modern and Creative themes must include qualification fields in their About components:

```tsx
// Helper function for qualification validation
const shouldShowQualificationError = (): boolean => {
  const qualificationError = getFieldError('qualifications');
  return !!(qualificationError && !data.qualification1 && !data.qualification2);
};

// Conditional rendering for qualifications
{(isEditing || data.qualification1 || data.qualification2) && (
  <div className="theme-qualifications">
    <h3>My Qualifications</h3>
    <div className="theme-qualifications-list">
      {(isEditing || data.qualification1) && (
        <div className="theme-qualification-item">
          <EditableText
            isEditing={isEditing}
            initialValue={data.qualification1 || ""}
            placeholder="e.g., Bachelor's in Computer Science"
            hasError={shouldShowQualificationError()}
            errorMessage={shouldShowQualificationError() ? getFieldError('qualifications') : undefined}
            onSave={(value) => handleUpdate('qualification1', value)}
          />
        </div>
      )}

      {(isEditing || data.qualification2) && (
        <div className="theme-qualification-item">
          <EditableText
            isEditing={isEditing}
            initialValue={data.qualification2 || ""}
            placeholder="e.g., 5+ years experience or certification"
            hasError={shouldShowQualificationError()}
            errorMessage={shouldShowQualificationError() && !data.qualification1 ? getFieldError('qualifications') : undefined}
            onSave={(value) => handleUpdate('qualification2', value)}
          />
        </div>
      )}
    </div>
  </div>
)}
```

#### Validation Best Practices:

- **Conditional Rendering**: Hide empty optional fields in published view
- **Error Display**: Show validation errors only when relevant
- **User Experience**: Provide clear, helpful error messages
- **Performance**: Use validation context efficiently to avoid unnecessary re-renders

### 11. Summary

- Themes are modular, dual-mode, and easy to extend.
- Use CSS variables for future customization.
- **Integrate validation system** for real-time user feedback.
- **Include qualification fields** in About components.
- Register and test your theme for both editing and export.
