# Profolify API Reference

This document details all API endpoints, data models, and integration points for Profolify Version 4.0 with profession-specific architecture support.

---

## 1. API Structure

- **Next.js API Routes**: For server-side logic (theme compilation, migration, utilities).
- **Client SDKs**: Direct use of Firebase (Firestore, Auth) and Cloudinary.
- **Export System**: Client-side only, no server endpoint.

---

## 2. API Endpoints

### Theme Compilation

**POST `/api/sync-themes`**  
- **Purpose**: Compile and sync all theme CSS files.
- **Returns**: `{ success: boolean, message: string }`
- **Usage**: Build process and development.

### Migration

**POST `/api/migrate-slugs`**  
- **Purpose**: Migrate portfolio slugs to a clean format.
- **Returns**: Success/error response.
- **Usage**: One-time migration for existing portfolios.

---

## 3. Client-Side APIs

### Firebase User Management

- **getUser(uid: string): Promise<User | null>**
- **createOrUpdateUser(firebaseUser): Promise<User>**
- **updateUserProfile(uid, updates): Promise<void>**
- **deleteUserAccount(uid, firebaseUser): Promise<void>**

### Portfolio Management

- **getPortfolio(userId: string): Promise<AnyPortfolioData | null>**
- **createPortfolioFromTemplate({ userId, userEmail, templateId }): Promise<PortfolioData>**
- **🆕 createPortfolioFromTemplateWithProfession({ userId, userEmail, templateId, professionType }): Promise<AnyPortfolioData>**
- **updatePortfolio({ userId, data }): Promise<void>**
- **deletePortfolio(userId: string): Promise<void>**
- **getPortfolioBySlug(slug: string): Promise<AnyPortfolioData | null>**
- **uploadFile(file: File): Promise<string>** (Cloudinary)

### 🆕 Profession-Specific Portfolio Creation

```typescript
// Create portfolio with profession-specific data structure
const portfolio = await createPortfolioFromTemplateWithProfession({
  userId: 'user123',
  userEmail: '<EMAIL>',
  templateId: 'modern',
  professionType: 'doctor', // Creates MedicalPortfolioData
});
```

### Theme Registry

- **getThemeComponent(templateId: string): React.ComponentType | null**
- **getAvailableThemes(): ThemeInfo[]**

---

## 4. Data Models

### User

```typescript
interface User {
  uid: string;
  email: string;
  displayName?: string;
  photoURL?: string;
  plan?: 'free' | 'pro';
  createdAt?: Date;
  updatedAt?: Date;
  // ...future: stripeCustomerId, customDomain, etc.
}
```

### 🆕 AnyPortfolioData (Union Type)

```typescript
// Union type supporting all portfolio types
type AnyPortfolioData =
  | PortfolioData
  | MedicalPortfolioData
  | ITPortfolioData
  | CreativePortfolioData
  | TravelPortfolioData;
```

### Base Portfolio Interface

```typescript
interface BasePortfolioData {
  id: string;
  userId: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  themeCategory: ThemeCategory;
  professionType: ProfessionType;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  contactEmail: string;
  email?: string;
  phone?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### General PortfolioData (Backward Compatible)

```typescript
interface PortfolioData extends BasePortfolioData {
  themeCategory: 'general';
  professionType: 'general';
  qualifications?: string;
  qualification1?: string;
  qualification2?: string;
  qualification3?: string;
  qualification4?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
}
```

### 🆕 Medical Portfolio Data

```typescript
interface MedicalPortfolioData extends BasePortfolioData {
  themeCategory: 'healthcare';
  professionType: 'doctor' | 'nurse';
  medicalLicense?: string;
  specializations: string[];
  certifications: string[];
  education: {
    degree: string;
    institution: string;
    year: string;
    location?: string;
  }[];
  experiences: MedicalExperience[];
  skills: Skill[];
  publications?: {
    id: string;
    title: string;
    journal: string;
    year: string;
    url?: string;
  }[];
  awards?: string[];
}
```

### 🆕 IT Portfolio Data

```typescript
interface ITPortfolioData extends BasePortfolioData {
  themeCategory: 'technology';
  professionType: 'it-professional';
  qualification1?: string;
  qualification2?: string;
  projects: ITProject[];
  experiences: Experience[];
  skills: Skill[];
  certifications?: string[];
  openSourceContributions?: {
    id: string;
    projectName: string;
    description: string;
    url: string;
    role: string;
  }[];
}
```

### Project, Experience, Skill, SocialLinks

```typescript
interface Project {
  id: string;
  title: string;
  description: string;
  url?: string;
  liveUrl?: string;
  imageUrl?: string;
}

interface Experience {
  id: string;
  role: string;
  company: string;
  duration: string;
  description?: string;
  location?: string;
  companyUrl?: string;
}

interface Skill {
  id: string;
  name: string;
  category?: string;
}

type SocialLinks = {
  github?: string;
  linkedin?: string;
  twitter?: string;
  // etc.
};
```

### 5. Example Usage

Get User

```typescript
import { getUser } from '@/lib/user-api';
const user = await getUser('uid123');
```

Create Portfolio

```typescript
import { createPortfolioFromTemplate } from '@/lib/portfolio-api';
const portfolio = await createPortfolioFromTemplate({ userId, userEmail, templateId });
```

Upload File

```typescript
import { uploadFile } from '@/lib/portfolio-api';
const url = await uploadFile(file);
```

### 6. Error Handling

- All API calls throw on error; use try/catch for user feedback.
- Firestore rules enforce user isolation and data security.
- Auth errors are surfaced to the UI for re-authentication.

### 7. Integration Notes

- Authentication: All sensitive operations require a valid Firebase Auth session.
- Export: The export system is client-side only; no server endpoint is needed.
- Extending: Add new API routes in app/api/ for server-side logic, or use Firebase Functions (Blaze tier).
