# Profolify System Architecture

## Overview

Profolify is a modern, Jamstack-based portfolio builder with profession-specific and organization-specific theme support.
It leverages a decoupled frontend (Next.js) and managed backend services (Firebase, Cloudinary) for performance, scalability, and security.

## 🆕 Architecture Version 2.0 - Profession-Specific Themes

As of Version 2.0, Profolify has been restructured to support profession-specific and organization-specific portfolio themes with different data structures, moving beyond the general one-size-fits-all approach.

---

## 1. High-Level System Diagram

```mermaid
graph TD
    subgraph User Facing
        A[Next.js Frontend] --> B{Routing & Middleware}
        A --> C{Client-Side State}
    end

    subgraph Backend Services
        D[Firebase] --> E[Firestore Database]
        D --> F[Firebase Authentication]
        G[Cloudinary] --> H[Image & File Storage]
    end

    subgraph Deployment
        I[Vercel] --> A
    end

    B --> F
    C --> A
    A --> E
    A --> H
```

## 2. Profession-Specific Architecture

### 2.1 Type Hierarchy

Profolify uses a three-level hierarchy for organizing themes and data structures:

```
ThemeCategory (Industry/Domain)
├── ProfessionType (Specific Job Role)
└── OrganizationType (Institution Type)
```

### 2.2 Theme Categories

- **general**: General-purpose portfolios
- **healthcare**: Medical professionals (doctors, nurses)
- **technology**: IT professionals, developers
- **creative**: Photographers, designers, artists
- **business**: Business analysts, consultants
- **education**: Teachers, educators
- **organization**: Company/institution portfolios

### 2.3 Data Structure Flexibility

Each profession gets its own data interface:

```typescript
// Healthcare professionals
interface MedicalPortfolioData {
  themeCategory: 'healthcare';
  professionType: 'doctor' | 'nurse';
  medicalLicense?: string;
  specializations: string[];
  certifications: string[];
  // ... medical-specific fields
}

// IT professionals
interface ITPortfolioData {
  themeCategory: 'technology';
  professionType: 'it-professional';
  projects: ITProject[];
  certifications?: string[];
  // ... tech-specific fields
}
```

### 2.4 Type Safety & Validation

- **Union Types**: `AnyPortfolioData` handles all portfolio types
- **Type Guards**: Safe property access with runtime checking
- **Conditional Validation**: Different validation rules per profession
- **Backward Compatibility**: Existing portfolios work seamlessly

## 3. Authentication & Routing

- Firebase Auth: Handles Google OAuth and user sessions.
- Next.js Middleware: Protects routes by checking for a firebaseIdToken cookie.
- Client Context: Zustand and React Context provide user state to all components.

#### Flow:

- User visits a protected route (e.g., /dashboard).
- Middleware checks for auth cookie; redirects to /login if missing.
- On login, Firebase returns an idToken, which is set as a cookie.
- Authenticated users are redirected to their dashboard.


## 3. State Management

- #### Server State: TanStack Query for Firestore data (portfolios, users).
- #### Editor State: React Context + useReducer for complex, local editing state.
- #### Global UI State: Zustand for simple, global state (auth, modals, etc).


## 4. Theming & Editor Engine

- Dual CSS Architecture
- Development: Each theme component has its own CSS file, imported into a modular file for hot-reloading.
- Production: A script compiles all modular CSS into a single file for performance and export.

- Dual-Mode Component Pattern
    Every theme component receives an isEditing prop.
    If true, renders editable fields and editor UI.
    If false, renders static, public-facing HTML.

## 5. Export System

- Live DOM Capture: Uses a hidden iframe to load the public portfolio, captures the rendered HTML, cleans it, and packages it with CSS/assets.
- JSZip: Bundles everything into a downloadable ZIP.
Zero server load: All export logic is client-side.

## 6. Deployment

- Vercel: Handles build, deployment, and global CDN.
- Firebase: Manages authentication and real-time database.
- Cloudinary: Optimizes and delivers images.


## 7. Extensibility & Scalability

- Theme System: Add new themes by following the modular pattern.
- API Routes: Add new endpoints in app/api/ for server-side logic.
- Cloud Functions: (Blaze tier) For atomic, secure backend operations.
- Custom Domains: (Planned) Use Vercel’s API for user-owned domains.
- Premium Features: Stripe integration for payments, premium themes, and more.

## 8. Security

- Auth Middleware: All private routes are protected.
- Firestore Rules: Only authenticated users can access their own data.
- No server secrets in client: All sensitive logic is server-side or in managed services.

## 9. Diagrams & Flowcharts

See README.md for a summary diagram.
See docs/EXPORT_SYSTEM_SUMMARY.md for export flow.

## 10. How to Extend

- Add a Theme: See THEME_DEVELOPMENT.md
- Add a Feature: Use Next.js API routes or Firebase Functions.
- Scale: Move user metadata to a dedicated users collection, add premium features, and enable custom domains.

## 11. Summary
Profolify’s architecture is modern, modular, and ready for scale.
It’s easy to extend, secure by default, and optimized for both developer and user experience.

