# Profession-Specific Architecture Guide

## Overview

Profolify Version 2.0 introduces a revolutionary profession-specific and organization-specific theme architecture that allows for tailored portfolio experiences based on user professions and organizational needs.

---

## Architecture Hierarchy

### Three-Level System

```
🏢 ThemeCategory (Industry/Domain)
   ├── 👤 ProfessionType (Specific Job Role)
   └── 🏛️ OrganizationType (Institution Type)
```

### Type Definitions

```typescript
// Level 1: Theme Categories (Broad Domains)
export type ThemeCategory = 
  | 'general' 
  | 'healthcare' 
  | 'technology' 
  | 'creative' 
  | 'business' 
  | 'education' 
  | 'organization';

// Level 2a: Profession Types (Specific Roles)
export type ProfessionType = 
  | 'general' 
  | 'doctor' 
  | 'nurse' 
  | 'it-professional' 
  | 'photographer' 
  | 'designer' 
  | 'teacher' 
  | 'business-analyst' 
  | 'traveller';

// Level 2b: Organization Types (Institution Types)
export type OrganizationType = 
  | 'general' 
  | 'hospital' 
  | 'tech-company' 
  | 'design-agency' 
  | 'educational-institution' 
  | 'non-profit';
```

---

## Data Structure Architecture

### Base Portfolio Interface

All portfolio types extend from a common base:

```typescript
export interface BasePortfolioData {
  id: string;
  userId: string;
  isPublished: boolean;
  slug: string;
  templateId: string;
  themeCategory: ThemeCategory;
  professionType: ProfessionType;
  userName: string;
  profession: string;
  about?: string;
  bio?: string;
  profileImageUrl?: string;
  resumeUrl?: string;
  contactEmail: string;
  email?: string;
  phone?: string;
  githubUrl?: string;
  linkedinUrl?: string;
  twitterUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Profession-Specific Interfaces

#### General Portfolio (Backward Compatible)
```typescript
export interface PortfolioData extends BasePortfolioData {
  themeCategory: 'general';
  professionType: 'general';
  qualifications?: string;
  qualification1?: string;
  qualification2?: string;
  qualification3?: string;
  qualification4?: string;
  projects: Project[];
  experiences: Experience[];
  skills: Skill[];
}
```

#### Medical Professional Portfolio
```typescript
export interface MedicalPortfolioData extends BasePortfolioData {
  themeCategory: 'healthcare';
  professionType: 'doctor' | 'nurse';
  medicalLicense?: string;
  specializations: string[];
  certifications: string[];
  education: {
    degree: string;
    institution: string;
    year: string;
    location?: string;
  }[];
  experiences: MedicalExperience[];
  skills: Skill[];
  publications?: {
    id: string;
    title: string;
    journal: string;
    year: string;
    url?: string;
  }[];
  awards?: string[];
}
```

#### IT Professional Portfolio
```typescript
export interface ITPortfolioData extends BasePortfolioData {
  themeCategory: 'technology';
  professionType: 'it-professional';
  qualification1?: string;
  qualification2?: string;
  projects: ITProject[];
  experiences: Experience[];
  skills: Skill[];
  certifications?: string[];
  openSourceContributions?: {
    id: string;
    projectName: string;
    description: string;
    url: string;
    role: string;
  }[];
}
```

#### Creative Professional Portfolio
```typescript
export interface CreativePortfolioData extends BasePortfolioData {
  themeCategory: 'creative';
  professionType: 'photographer' | 'designer';
  portfolio: CreativeProject[];
  experiences: Experience[];
  skills: Skill[];
  awards?: string[];
  exhibitions?: {
    id: string;
    name: string;
    venue: string;
    date: string;
    description?: string;
  }[];
}
```

#### Travel Professional Portfolio
```typescript
export interface TravelPortfolioData extends BasePortfolioData {
  themeCategory: 'general';
  professionType: 'traveller';
  travelExperiences: TravelExperience[];
  skills: Skill[];
  languages?: string[];
  countries?: string[];
  travelStyle?: 'solo' | 'group' | 'business' | 'volunteer';
}
```

---

## Union Type & Type Guards

### Union Type
```typescript
export type AnyPortfolioData = 
  | PortfolioData 
  | MedicalPortfolioData 
  | ITPortfolioData 
  | CreativePortfolioData 
  | TravelPortfolioData;
```

### Type Guards
```typescript
export function isGeneralPortfolio(data: AnyPortfolioData): data is PortfolioData {
  return data.themeCategory === 'general' && data.professionType === 'general';
}

export function isMedicalPortfolio(data: AnyPortfolioData): data is MedicalPortfolioData {
  return data.themeCategory === 'healthcare' && 
         (data.professionType === 'doctor' || data.professionType === 'nurse');
}

export function isITPortfolio(data: AnyPortfolioData): data is ITPortfolioData {
  return data.themeCategory === 'technology' && data.professionType === 'it-professional';
}

export function isCreativePortfolio(data: AnyPortfolioData): data is CreativePortfolioData {
  return data.themeCategory === 'creative' && 
         (data.professionType === 'photographer' || data.professionType === 'designer');
}

export function isTravelPortfolio(data: AnyPortfolioData): data is TravelPortfolioData {
  return data.themeCategory === 'general' && data.professionType === 'traveller';
}
```

---

## Factory Pattern for Data Creation

### Default Data Factory
```typescript
export function createDefaultPortfolioData(
  professionType: ProfessionType,
  userId: string,
  templateId: string
): AnyPortfolioData {
  const baseData = {
    id: '',
    userId,
    isPublished: false,
    slug: '',
    templateId,
    userName: '',
    profession: '',
    contactEmail: '',
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  switch (professionType) {
    case 'doctor':
    case 'nurse':
      return {
        ...baseData,
        themeCategory: 'healthcare',
        professionType,
        specializations: [],
        certifications: [],
        education: [],
        experiences: [],
        skills: [],
      } as MedicalPortfolioData;

    case 'it-professional':
      return {
        ...baseData,
        themeCategory: 'technology',
        professionType,
        projects: [],
        experiences: [],
        skills: [],
      } as ITPortfolioData;

    case 'photographer':
    case 'designer':
      return {
        ...baseData,
        themeCategory: 'creative',
        professionType,
        portfolio: [],
        experiences: [],
        skills: [],
      } as CreativePortfolioData;

    case 'traveller':
      return {
        ...baseData,
        themeCategory: 'general',
        professionType,
        travelExperiences: [],
        skills: [],
      } as TravelPortfolioData;

    default:
      return {
        ...baseData,
        themeCategory: 'general',
        professionType: 'general',
        projects: [],
        experiences: [],
        skills: [],
      } as PortfolioData;
  }
}
```

---

## Theme Registry Integration

### Enhanced Theme Configuration
```typescript
export interface ThemeConfig {
  id: string;
  name: string;
  description: string;
  component: React.ComponentType<any>;
  preview: string;
  modularCss: string;
  compiledCss: string;
  isPremium: boolean;
  // 🆕 New fields for profession targeting
  themeCategory: ThemeCategory;
  professionTypes: ProfessionType[];
  organizationTypes?: OrganizationType[];
}
```

### Theme Filtering Functions
```typescript
export function getThemesByThemeCategory(category: ThemeCategory): ThemeConfig[] {
  return THEME_REGISTRY.filter(theme => theme.themeCategory === category);
}

export function getThemesByProfession(professionType: ProfessionType): ThemeConfig[] {
  return THEME_REGISTRY.filter(theme => 
    theme.professionTypes.includes(professionType)
  );
}

export function getFilteredThemes(
  category?: ThemeCategory,
  professionType?: ProfessionType,
  organizationType?: OrganizationType
): ThemeConfig[] {
  return THEME_REGISTRY.filter(theme => {
    if (category && theme.themeCategory !== category) return false;
    if (professionType && !theme.professionTypes.includes(professionType)) return false;
    if (organizationType && !theme.organizationTypes?.includes(organizationType)) return false;
    return true;
  });
}
```

---

## Benefits & Scalability

### Key Advantages

1. **Type Safety**: Full TypeScript support with compile-time checking
2. **Scalability**: Easy to add new professions without breaking existing code
3. **Flexibility**: Themes can target specific professions or be multi-profession
4. **Backward Compatibility**: Existing portfolios continue to work seamlessly
5. **Data Integrity**: Each profession gets appropriate data structures
6. **User Experience**: Tailored interfaces for different professional needs

### Adding New Professions

To add a new profession (e.g., "lawyer"):

1. **Update Types**:
```typescript
export type ProfessionType = '...' | 'lawyer';
export type ThemeCategory = '...' | 'legal';
```

2. **Create Data Interface**:
```typescript
export interface LegalPortfolioData extends BasePortfolioData {
  themeCategory: 'legal';
  professionType: 'lawyer';
  barAdmission?: string;
  practiceAreas: string[];
  cases?: LegalCase[];
}
```

3. **Update Union Type**:
```typescript
export type AnyPortfolioData = PortfolioData | MedicalPortfolioData | LegalPortfolioData;
```

4. **Add Type Guard**:
```typescript
export function isLegalPortfolio(data: AnyPortfolioData): data is LegalPortfolioData {
  return data.themeCategory === 'legal' && data.professionType === 'lawyer';
}
```

5. **Update Factory Function**:
```typescript
case 'lawyer':
  return {
    ...baseData,
    themeCategory: 'legal',
    professionType,
    practiceAreas: [],
  } as LegalPortfolioData;
```

This architecture provides a robust, scalable foundation for profession-specific portfolio themes while maintaining clean separation of concerns and type safety throughout the application.
