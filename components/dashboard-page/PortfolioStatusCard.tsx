"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import {
  ExternalLink,
  Download,
  Copy,
  Share2,
  Loader2,
  Check,
  Twitter,
  Facebook,
  Linkedin,
  Mail,
  Palette,
  MoreVertical,
  Trash2,
} from "lucide-react"
import { toast } from "sonner"
import { getThemeById } from "@/themes/theme-registry"
import Image from "next/image"

interface PortfolioStatusCardProps {
  portfolio: {
    isPublished: boolean
    slug: string
    templateId: string
  }
  onExport?: () => void
  isExporting?: boolean
  onChangeTheme?: () => void
  onDelete?: () => void
}

export default function PortfolioStatusCard({
  portfolio,
  onExport,
  isExporting = false,
  onChangeTheme,
  onDelete
}: PortfolioStatusCardProps) {
  const [copied, setCopied] = useState(false)

  const fullUrl = `${typeof window !== "undefined" ? window.location.origin : "https://www.profolify.com"}/${portfolio.slug}`
  const shortUrl = `profolify.com/${portfolio.slug}`

  // Get current theme information
  const currentTheme = getThemeById(portfolio.templateId)

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl)
      setCopied(true)
      toast.success("Link copied to clipboard!")
      setTimeout(() => setCopied(false), 2000)
    } catch {
      toast.error("Failed to copy link. Please try again.",)
    }
  }

  const handleShare = (platform: string) => {
    const text = "Check out my portfolio!"
    const encodedUrl = encodeURIComponent(fullUrl)
    const encodedText = encodeURIComponent(text)

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodedText}&url=${encodedUrl}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodedUrl}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodedUrl}`,
      email: `mailto:?subject=${encodedText}&body=${encodedText}%20${encodedUrl}`,
    }

    if (shareUrls[platform as keyof typeof shareUrls]) {
      window.open(shareUrls[platform as keyof typeof shareUrls], "_blank", "width=600,height=400")
    }
  }

  return (
    <Card className="w-full bg-gradient-to-tr from-gray-50 to-white border-gray-200/80 rounded-2xl shadow-sm">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="text-xl font-bold tracking-tight">Portfolio Status</h3>
            <p className="text-sm text-muted-foreground">
              Your portfolio is currently {portfolio.isPublished ? "published and live." : "a draft. Edit and publish it to share with the world!"}
            </p>
          </div>
          <Badge
            variant={portfolio.isPublished ? "success" : "secondary"}
            className="text-sm"
          >
            {portfolio.isPublished ? "Live" : "Draft"}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {currentTheme && (
          <div className="space-y-3">
            <label className="text-sm font-medium text-muted-foreground">Current Theme</label>
            <div className="flex items-center gap-4 p-3 bg-white rounded-xl border border-gray-200/80">
              <div className="relative w-20 h-16 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={currentTheme.preview || '/thumbnails/default-theme.jpg'}
                  alt={`${currentTheme.name} preview`}
                  fill
                  className="object-cover"
                />
              </div>
              <div className="flex-1">
                <h4 className="font-semibold text-md">{currentTheme.name}</h4>
                <p className="text-sm text-muted-foreground line-clamp-2">{currentTheme.description}</p>
              </div>
              <Button onClick={onChangeTheme} variant="outline" size="sm">
                <Palette className="mr-2 h-4 w-4" />
                Change
              </Button>
            </div>
          </div>
        )}

        {portfolio.isPublished && (
          <div className="space-y-3">
            <label className="text-sm font-medium text-muted-foreground">Share Your Portfolio</label>
            <div className="flex items-center gap-2">
              <Input readOnly value={shortUrl} className="flex-1 font-mono text-sm bg-gray-100 border-gray-300 focus-visible:ring-brandPrimary" />
              <Button size="icon" variant="ghost" onClick={handleCopyLink} className="h-9 w-9 hover:bg-gray-200">
                {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4 text-gray-600" />}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="icon" className="h-9 w-9">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  <DropdownMenuItem onClick={() => handleShare("twitter")}><Twitter className="h-4 w-4 mr-2" />Twitter</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("facebook")}><Facebook className="h-4 w-4 mr-2" />Facebook</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("linkedin")}><Linkedin className="h-4 w-4 mr-2" />LinkedIn</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleShare("email")}><Mail className="h-4 w-4 mr-2" />Email</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}

        <div className="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200/80">
          <Button asChild size="lg" className="flex-1">
            <Link href="/portfolio">
              Edit Portfolio
            </Link>
          </Button>
          <div className="flex gap-3">
            {portfolio.isPublished && (
              <Button asChild variant="outline" size="lg" className="flex-1">
                <Link href={`/${portfolio.slug}`} target="_blank">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  View
                </Link>
              </Button>
            )}
            <Button
              onClick={onExport}
              variant="outline"
              size="lg"
              className="flex-1"
              disabled={isExporting || !portfolio.isPublished}
            >
              {isExporting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Download className="mr-2 h-4 w-4" />}
              Export
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="h-12 w-12">
                  <MoreVertical className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onDelete} className="text-red-600 focus:text-red-600">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
