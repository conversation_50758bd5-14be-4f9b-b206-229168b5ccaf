"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Loader2, Check, Edit, Palette, Building } from "lucide-react";
import { toast } from "sonner";
import {
  getThemesByThemeCategory,
  getThemesByProfession,
  getAllThemeCategories,
  getAllProfessionTypes,
  ThemeConfig
} from "@/themes/theme-registry";
import { updatePortfolioTheme, createPortfolioFromTemplateWithProfession } from "@/lib/portfolio-api";
import { User, ThemeCategory, ProfessionType } from "@/lib/types";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
  themeCategory?: ThemeCategory;
  professionType?: ProfessionType;
}

interface CategoryThemeGridProps {
  user: User | null;
  portfolio: Portfolio | null;
}

// Main category configuration (Personal vs Organization)
const mainCategories = [
  {
    id: 'personal',
    label: 'Personal',
    icon: Palette,
    description: 'Individual portfolio themes'
  },
  {
    id: 'organization',
    label: 'Organization',
    icon: Building,
    description: 'Company and institution themes'
  }
];

// Removed categoryIcons as we're not using sub-categories anymore

const professionLabels: Record<ProfessionType, string> = {
  general: 'General',
  doctor: 'Doctor',
  nurse: 'Nurse',
  'it-professional': 'IT Professional',
  photographer: 'Photographer',
  designer: 'Designer',
  teacher: 'Teacher',
  'business-analyst': 'Business Analyst',
  traveller: 'Travel Blogger',
};

export default function CategoryThemeGrid({ user, portfolio }: CategoryThemeGridProps) {
  const [loadingThemeId, setLoadingThemeId] = useState<string | null>(null);
  const [selectedMainCategory, setSelectedMainCategory] = useState<'personal' | 'organization'>('personal');
  // Removed selectedCategory as we're using profession-based filtering now
  const [selectedProfession, setSelectedProfession] = useState<ProfessionType>('general');

  const queryClient = useQueryClient();
  const router = useRouter();

  // For existing portfolio - theme switching
  const switchThemeMutation = useMutation({
    mutationFn: async ({ portfolioId, templateId }: { portfolioId: string; templateId: string }) => {
      return updatePortfolioTheme(portfolioId, templateId);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);
    },
    onSuccess: async () => {
      await Promise.all([
        queryClient.invalidateQueries({ queryKey: ['portfolios', user?.uid] }),
        queryClient.refetchQueries({ queryKey: ['portfolios', user?.uid] })
      ]);
      toast.success("Theme applied successfully!");
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      toast.error(`Failed to switch theme: ${error.message}`);
      setLoadingThemeId(null);
    }
  });

  // For new portfolio creation
  const createPortfolioMutation = useMutation({
    mutationFn: async ({ user, templateId, professionType }: {
      user: User;
      templateId: string;
      professionType: ProfessionType;
    }) => {
      return createPortfolioFromTemplateWithProfession(user, templateId, professionType);
    },
    onMutate: (variables) => {
      setLoadingThemeId(variables.templateId);

      // Navigate immediately when creation starts (like ThemeGrid does)
      console.log('🚀 Starting portfolio creation, navigating to editor...');
      router.push('/portfolio?creating=true');
      toast.loading("Creating your portfolio...", { id: 'portfolio-creation' });
    },
    onSuccess: (newPortfolio) => {
      console.log('✅ Portfolio created successfully:', newPortfolio);

      // Update cache after successful creation
      queryClient.setQueryData(['portfolios', user?.uid], [newPortfolio]);

      // Dismiss loading toast and show success
      toast.dismiss('portfolio-creation');
      toast.success("Portfolio created successfully!");

      // Clear loading state
      setLoadingThemeId(null);
    },
    onError: (error: Error) => {
      console.error('❌ Portfolio creation failed:', error);

      // Dismiss loading toast and show error
      toast.dismiss('portfolio-creation');
      toast.error(`Failed to create portfolio: ${error.message}`);
      setLoadingThemeId(null);

      // Navigate back to dashboard on error
      router.push('/dashboard');
    }
  });

  const handleThemeAction = (themeId: string, professionType: ProfessionType = 'general') => {
    if (!user) {
      toast.error("You must be logged in to select a theme.");
      return;
    }

    if (portfolio) {
      // Switch theme for existing portfolio
      switchThemeMutation.mutate({ portfolioId: portfolio.id, templateId: themeId });
    } else {
      // Create new portfolio with selected theme and profession type
      createPortfolioMutation.mutate({ user, templateId: themeId, professionType });
    }
  };

  const renderThemeCard = (theme: ThemeConfig) => {
    const isCurrentTheme = portfolio?.templateId === theme.id;
    const isLoading = loadingThemeId === theme.id;

    return (
      <Card key={theme.id} className="group relative overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-105 border-gray-200/80 rounded-2xl">
        <div className="relative aspect-video overflow-hidden rounded-t-2xl">
          {theme.preview && (
            <Image
              src={theme.preview}
              alt={`${theme.name} theme preview`}
              fill
              className="object-cover transition-transform duration-300"
            />
          )}
          <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
          {isCurrentTheme && (
            <Badge variant="default" className="absolute top-3 right-3 bg-brandPrimary text-white">
              <Check className="h-3 w-3 mr-1" />
              Current
            </Badge>
          )}
        </div>

        <CardContent className="p-5">
          <h3 className="font-bold text-xl mb-1">{theme.name}</h3>
          <p className="text-md text-muted-foreground mb-4 h-12">{theme.description}</p>

          <div className="flex flex-wrap gap-2 mb-4">
            {theme.tags?.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-sm">
                {tag}
              </Badge>
            ))}
            {theme.isPremium && (
              <Badge variant="default" className="text-sm bg-gradient-to-r from-purple-500 to-pink-500">
                Premium
              </Badge>
            )}
          </div>

          {portfolio ? (
            <Button
              onClick={() => handleThemeAction(theme.id)}
              disabled={isLoading || isCurrentTheme}
              size="lg"
              variant={isCurrentTheme ? "outline" : "default"}
              className="w-full font-semibold text-lg"
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <>
                  <Edit className="h-5 w-5 mr-2" />
                  Switch Theme
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={() => handleThemeAction(theme.id, selectedProfession)}
              disabled={isLoading}
              size="lg"
              className="w-full font-semibold text-lg"
            >
              {isLoading ? (
                <Loader2 className="h-5 w-5 animate-spin" />
              ) : (
                <>
                  <Edit className="h-5 w-5 mr-2" />
                  Create with {theme.name}
                </>
              )}
            </Button>
          )}
        </CardContent>
      </Card>
    );
  };

  const categories = getAllThemeCategories();
  const professionTypes = getAllProfessionTypes();

  // Get themes based on selected filters
  const getFilteredThemes = () => {
    if (selectedMainCategory === 'organization') {
      return getThemesByThemeCategory('organization');
    }

    // For personal themes - show all personal themes based on profession
    if (selectedMainCategory === 'personal') {
      // Get themes by profession (which includes general themes)
      const professionThemes = getThemesByProfession(selectedProfession);

      // Also get themes from specific categories if they support the profession
      const categoryThemes = categories
        .filter(cat => cat !== 'organization' && cat !== 'general')
        .flatMap(cat => getThemesByThemeCategory(cat))
        .filter(theme => theme.professionTypes.includes(selectedProfession));

      // Combine and deduplicate
      const allThemes = [...professionThemes, ...categoryThemes];
      return allThemes.filter((theme, index, self) =>
        index === self.findIndex(t => t.id === theme.id)
      );
    }

    return [];
  };

  const filteredThemes = getFilteredThemes();

  return (
    <div className="space-y-8">
      <div className="text-center max-w-2xl mx-auto">
        <h2 className="text-3xl font-bold tracking-tight">
          {portfolio ? 'Switch Theme' : 'Choose Your Starting Theme'}
        </h2>
        <p className="mt-2 text-lg text-muted-foreground">
          {portfolio
            ? 'Select a new look for your portfolio.'
            : 'Pick a theme to match your professional style. You can always change it later.'
          }
        </p>
      </div>

      <Tabs value={selectedMainCategory} onValueChange={(value) => setSelectedMainCategory(value as 'personal' | 'organization')} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8 h-auto p-1.5 bg-gray-100 rounded-xl">
          {mainCategories.map((category) => {
            const Icon = category.icon;
            return (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="flex items-center justify-center gap-3 p-3 h-auto text-left data-[state=active]:bg-white data-[state=active]:text-foreground data-[state=active]:shadow-md border-0 rounded-lg transition-all duration-200"
              >
                <Icon className="h-6 w-6 flex-shrink-0 text-brandPrimary" />
                <div className="text-left flex-1 min-w-0">
                  <div className="font-semibold text-md">{category.label}</div>
                  <div className="text-sm text-muted-foreground hidden sm:block mt-0.5">{category.description}</div>
                </div>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="personal" className="mt-6 space-y-6 focus-visible:outline-none">
          <div className="p-6 bg-white rounded-2xl border border-gray-200/80">
            <h3 className="text-xl font-bold mb-2">Select Your Profession</h3>
            <p className="text-md text-muted-foreground mb-6">Choose your profession to see the most relevant themes.</p>
            <div className="flex flex-wrap gap-3">
              {professionTypes.map((profession) => (
                <Button
                  key={profession}
                  variant={selectedProfession === profession ? "default" : "outline"}
                  size="default"
                  onClick={() => setSelectedProfession(profession)}
                  className="transition-all duration-200 rounded-full px-6 py-3 text-base"
                >
                  {professionLabels[profession]}
                </Button>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="organization" className="mt-6 space-y-6 focus-visible:outline-none">
          <div className="text-center py-12 bg-white rounded-2xl border border-gray-200/80">
            <Building className="h-16 w-16 mx-auto text-gray-300 mb-4" />
            <h3 className="text-2xl font-bold mb-2">Organization Themes</h3>
            <p className="text-muted-foreground mb-4">Professional themes for companies and institutions.</p>
            <p className="text-md text-muted-foreground">Coming soon! We&apos;re crafting amazing themes for organizations.</p>
          </div>
        </TabsContent>

        <div className="mt-8">
          {filteredThemes.length > 0 ? (
            <div className="space-y-6">
              <div className="text-center">
                <h3 className="text-2xl font-bold">Available Themes</h3>
                <p className="text-md text-muted-foreground mt-1">
                  {selectedMainCategory === 'personal'
                    ? `Themes optimized for ${professionLabels[selectedProfession]}s`
                    : 'Professional themes for organizations'
                  }
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredThemes.map(renderThemeCard)}
              </div>
            </div>
          ) : (
            <div className="text-center py-16 bg-white rounded-2xl border border-gray-200/80">
              <Palette className="h-16 w-16 mx-auto text-gray-300 mb-4" />
              <h3 className="text-2xl font-bold mb-2">No Themes Available</h3>
              <p className="text-muted-foreground">
                {selectedMainCategory === 'organization'
                  ? 'Organization themes are coming soon!'
                  : `Themes for ${professionLabels[selectedProfession]}s are in development!`
                }
              </p>
            </div>
          )}
        </div>
      </Tabs>
    </div>
  );
}
