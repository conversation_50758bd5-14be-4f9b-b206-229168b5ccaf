"use client";

import { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Loader2,
  ExternalLink,
  Trash2,
  Copy,
  Check,
  Download,
  Edit,
} from "lucide-react";
import { getThemeById } from "@/themes/theme-registry";
import { toast } from "sonner";
import {
  TwitterShareButton,
  FacebookShareButton,
  LinkedinShareButton,
  TwitterIcon,
  FacebookIcon,
  LinkedinIcon,
} from "react-share";

interface Portfolio {
  id: string;
  templateId: string;
  isPublished: boolean;
  slug: string;
  userName: string;
}

interface CurrentPortfolioCardProps {
  portfolio: Portfolio;
  onExport?: () => void;
  isExporting?: boolean;
  onDelete?: () => void;
}

export default function CurrentPortfolioCard({
  portfolio,
  onExport,
  isExporting = false,
  onDelete
}: CurrentPortfolioCardProps) {
  const [copied, setCopied] = useState(false);
  const currentTheme = getThemeById(portfolio.templateId);

  // Generate portfolio URL
  const fullUrl = `${typeof window !== "undefined" ? window.location.origin : "https://profolify.com"}/${portfolio.slug}`;
  const shortUrl = `profolify.com/${portfolio.slug}`;

  const handleCopyLink = async () => {
    try {
      await navigator.clipboard.writeText(fullUrl);
      setCopied(true);
      toast.success("Link copied to clipboard!");
      setTimeout(() => setCopied(false), 2000);
    } catch {
      toast.error("Failed to copy link. Please try again.");
    }
  };

  const shareTitle = `Check out ${portfolio.userName}'s portfolio!`;
  const shareDescription = `Discover ${portfolio.userName}'s amazing work and projects.`;

  return (
    <div className="relative rounded-2xl border border-gray-200/80 bg-gradient-to-tr from-gray-50 to-white shadow-sm overflow-hidden">
      <div className="p-6 space-y-6">
        {/* Header Section */}
        <div className="flex items-start justify-between">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Current Portfolio</h3>
            <p className="text-sm text-gray-600 mt-1">
              Theme: <span className="font-semibold">{currentTheme?.name || "Unknown"}</span> - {currentTheme?.description || "A clean, modern theme."}
            </p>
          </div>
          <Badge
            variant={portfolio.isPublished ? "success" : "secondary"}
            className={`w-fit py-1 flex items-center gap-2 rounded-full px-3 text-sm ${portfolio.isPublished
              ? "bg-green-100 text-green-800 border-green-200"
              : "bg-yellow-100 text-yellow-800 border-yellow-200"
              }`}
          >
            {portfolio.isPublished ? "Published" : "Draft"}
          </Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Portfolio Preview */}
          <div className="relative group rounded-lg overflow-hidden border border-gray-200/80 shadow-inner">
            <div className="w-full aspect-[16/9] bg-gray-100">
              {currentTheme?.preview ? (
                <Image
                  src={currentTheme.preview}
                  alt={`${currentTheme.name} preview`}
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="bg-gray-200 h-full flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <div className="w-16 h-16 bg-gray-300 rounded-full mx-auto mb-2"></div>
                    <div className="text-sm font-medium">Portfolio Preview</div>
                  </div>
                </div>
              )}
            </div>
            {/* Edit Button - always visible */}
            <Button
              asChild
              variant="accent"
              className="absolute top-4 right-4 rounded-full shadow-lg h-10 w-10 p-0 flex items-center justify-center"
            >
              <Link href="/portfolio">
                <Edit className="h-4 w-4" />
              </Link>
            </Button>
          </div>

          {/* Right Column - Actions & Sharing */}
          <div className="space-y-6">
            <Button asChild size="lg" className="w-full rounded-sm " variant="accent">
              <Link href="/portfolio">
                <Edit className="mr-2 h-5 w-5" />
                Edit Portfolio
              </Link>
            </Button>
            {/* Action Buttons */}
            <div className="flex flex-wrap gap-3">

              {portfolio.isPublished && (
                <Button asChild variant="default" size="sm" className="flex-1 min-w-[120px]">
                  <Link href={`/${portfolio.slug}`} target="_blank">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Live
                  </Link>
                </Button>
              )}
              <Button
                onClick={onExport}
                size="sm"
                variant="outline"
                disabled={isExporting || !portfolio.isPublished}
                className="flex-1 min-w-[120px]"
              >
                {isExporting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Download className="mr-2 h-4 w-4" />
                )}
                Export
              </Button>
              <Button
                onClick={onDelete}
                variant="destructive"
                size="sm"
                className="flex-1 min-w-[120px]"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
            </div>

            {/* Shareable Links Section */}
            {portfolio.isPublished ? (
              <div className="space-y-3">
                <h4 className="text-md font-semibold text-gray-800">Share Your Portfolio</h4>
                <div className="flex items-center gap-2">
                  <Input readOnly value={shortUrl} className="flex-1 font-mono text-sm bg-gray-100 border-gray-300 focus-visible:ring-brandPrimary" />
                  <Button size="icon" variant="ghost" onClick={handleCopyLink} className="h-9 w-9 hover:bg-gray-200">
                    {copied ? <Check className="h-4 w-4 text-green-600" /> : <Copy className="h-4 w-4 text-gray-600" />}
                  </Button>
                </div>
                <div className="flex items-center gap-3 pt-2">
                  <TwitterShareButton url={fullUrl} title={shareTitle} className="transition-opacity hover:opacity-80">
                    <TwitterIcon size={32} round />
                  </TwitterShareButton>
                  <FacebookShareButton url={fullUrl} hashtag="#portfolio" className="transition-opacity hover:opacity-80">
                    <FacebookIcon size={32} round />
                  </FacebookShareButton>
                  <LinkedinShareButton url={fullUrl} title={shareTitle} summary={shareDescription} source="Profolify" className="transition-opacity hover:opacity-80">
                    <LinkedinIcon size={32} round />
                  </LinkedinShareButton>
                </div>
              </div>
            ) : (
              <div className="text-center bg-gray-100 p-6 rounded-lg border border-gray-200/80">
                <p className="text-sm text-gray-600 leading-relaxed">
                  Your portfolio is currently a draft. Publish it to share with the world.
                </p>
                <Button asChild size="sm" className="mt-4">
                  <Link href="/portfolio">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit and Publish
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
