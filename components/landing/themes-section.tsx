"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Download, Pa<PERSON>, Spark<PERSON> } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const themes = [
  {
    id: "modern-theme-v1",
    name: "Modern",
    description: "A sleek, dark-mode theme perfect for developers and designers.",
    image: "/thumbnails/Modern.png",
    category: "Dark Theme",
    badgeColor: "bg-slate-600",
  },
  {
    id: "creative-theme-v1",
    name: "Creative Minimalist",
    description: "A light, clean theme ideal for showcasing creative work.",
    image: "/thumbnails/Creative Minimalist.png",
    category: "Light Theme",
    badgeColor: "bg-blue-500",
  }
];

export function ThemesSection() {
  const router = useRouter();

  return (
    <section
      id="themes"
      className="py-24 lg:py-32 bg-backgroundPrimary relative overflow-hidden"
    >
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-1/4 left-0 w-full h-full bg-gradient-to-r from-brandPrimary/5 to-transparent blur-3xl"></div>
        <div className="absolute -bottom-1/4 right-0 w-full h-full bg-gradient-to-l from-brandAccent/5 to-transparent blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-3 px-6 py-3 rounded-2xl bg-brandPrimary/10 border border-brandPrimary/20 mb-8">
            <Palette className="w-5 h-5 text-brandPrimary" />
            <span className="text-sm font-bold text-brandPrimary">
              Professionally Designed Themes
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Find Your Perfect Style</span>
            <br />
            <span className="text-textPrimary">to Showcase Your Work</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Start with our mobile-responsive themes, each professionally optimized for SEO
            and ready to make your portfolio shine.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-start mb-20">
          {themes.map((theme) => (
            <div key={theme.id} className="rounded-2xl border border-borderPrimary/50 bg-backgroundSecondary/50 shadow-lg transition-all duration-300">
              <div className="relative w-full aspect-[16/10] overflow-hidden rounded-t-2xl">
                <Image
                  src={theme.image}
                  alt={`${theme.name} theme preview`}
                  fill
                  className="object-contain p-6 object-top transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <h3 className="text-2xl font-bold text-textPrimary mb-2">{theme.name}</h3>
                <p className="text-textSecondary mb-6">{theme.description}</p>
                <div className="flex items-center gap-4">
                  <Button
                    onClick={() => router.push("/login")}
                    variant="default"
                    className="w-full"
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Use Theme
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="relative bg-backgroundSecondary/50 rounded-2xl border border-borderPrimary p-8 lg:p-12 text-center shadow-xl shadow-brandPrimary/5">
          <div className="mb-6">
            <div className="w-16 h-16 bg-brandPrimary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Sparkles className="w-8 h-8 text-brandPrimary" />
            </div>
            <h3 className="text-3xl md:text-4xl font-bold mb-4">
              <span className="gradient-text">More Themes Coming Soon</span>
            </h3>
          </div>

          <p className="text-textSecondary text-lg mb-8 max-w-3xl mx-auto leading-relaxed">
            We&apos;re crafting more stunning themes, including designs for business professionals,
            creative agencies, and developers.
            <span className="text-brandPrimary font-semibold"> Join now for early access!</span>
          </p>

          <div className="flex justify-center">
            <Button
              onClick={() => router.push("/login")}
              variant="cta"
              size="lg"
              className="group"
            >
              <span className="flex items-center gap-2">
                Start Building Your Portfolio
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
