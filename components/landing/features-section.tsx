"use client";

import {
  Edit3,
  Share2,
  Palette,
  Smartphone,
  ArrowRight,
  Sparkles,
  Globe,
  Download,
} from "lucide-react";
import { Button } from "../ui/button";
import { useRouter } from "next/navigation";
import { AnimatedBorderCard } from "../ui/animated-border-card";

const features = [
  {
    icon: Edit3,
    title: "Free WYSIWYG Portfolio Builder",
    description:
      "Build professional portfolios with our intuitive editor. Click any text to edit in place. No coding required.",
    highlight: "No Code Required",
  },
  {
    icon: Download,
    title: "Export Static HTML Files",
    description:
      "Download your portfolio as a complete static HTML website. Host anywhere with pixel-perfect exports.",
    highlight: "Static HTML Export",
  },
  {
    icon: Palette,
    title: "Professional Portfolio Themes",
    description:
      "Choose from stunning, mobile-responsive themes. All are SEO-optimized for maximum impact.",
    highlight: "SEO-Optimized Themes",
  },
  {
    icon: Share2,
    title: "SEO-Optimized URLs & Publishing",
    description:
      "Generates clean, professional URLs from your name for better search engine visibility and instant publishing.",
    highlight: "SEO Optimized",
  },
  {
    icon: Smartphone,
    title: "Mobile-Responsive Design",
    description:
      "All portfolio themes are fully responsive and optimized for mobile, tablets, and desktops.",
    highlight: "Mobile Optimized",
  },
  {
    icon: Globe,
    title: "Professional Hosting & Sharing",
    description:
      "Instantly publish your portfolio online with professional URLs. Perfect for job applications and freelance projects.",
    highlight: "Instant Publishing",
  },
];

export function FeaturesSection() {
  const router = useRouter();

  return (
    <section id="features" className="relative py-24 lg:py-32 overflow-hidden bg-backgroundPrimary">
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/2 -translate-y-1/2 left-0 w-96 h-96 bg-brandAccent/5 rounded-full blur-3xl animate-pulse-slow"></div>
        <div className="absolute top-1/2 -translate-y-1/2 right-0 w-96 h-96 bg-brandSecondary/5 rounded-full blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="container relative mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandPrimary/10 border border-brandPrimary/20 mb-8">
            <Sparkles className="w-4 h-4 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Everything You Need
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Powerful Features,</span>
            <br />
            <span className="text-textPrimary">
              Effortless Portfolio Building
            </span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Create stunning portfolios with our intuitive editor, beautiful themes,
            and revolutionary export technology. All for free.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {features.map((feature, index) => {
            const IconComponent = feature.icon;
            const iconColors = [
              'bg-brandPrimary', 'bg-brandAccent', 'bg-brandSecondary',
              'bg-info', 'bg-warning', 'bg-success'
            ];
            const iconColor = iconColors[index % iconColors.length];

            return (
              <AnimatedBorderCard key={feature.title}>
                <div className="p-8">
                  <div className={`w-12 h-12 rounded-xl ${iconColor} flex items-center justify-center mb-6 shadow-lg shadow-brandPrimary/20`}>
                    <IconComponent className="w-6 h-6 text-white" />
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-xl font-bold text-textPrimary">
                      {feature.title}
                    </h3>
                    <p className="text-textSecondary leading-relaxed">
                      {feature.description}
                    </p>
                    <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-brandPrimary/10 border border-brandPrimary/20">
                      <div className={`w-2 h-2 rounded-full ${iconColor}`}></div>
                      <span className="text-xs font-semibold text-brandPrimary">
                        {feature.highlight}
                      </span>
                    </div>
                  </div>
                </div>
              </AnimatedBorderCard>
            );
          })}
        </div>

        <div className="text-center bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 rounded-2xl p-12 border border-brandPrimary/20 shadow-xl shadow-brandPrimary/10">
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Ready to Build Your Portfolio?</span>
          </h3>
          <p className="text-lg text-textSecondary max-w-2xl mx-auto mb-8">
            Create your professional portfolio in minutes. It’s free, powerful, and designed for you.
          </p>
          <Button
            variant="cta"
            size="lg"
            onClick={() => router.push("/login")}
            className="px-8 py-4 group"
          >
            <span className="flex items-center gap-2">
              Start Building Free
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
}
