import React from "react";
import Link from "next/link";
import { Github, Twitter, Mail } from "lucide-react";
import Image from "next/image";

export function LandingFooter() {
  const currentYear = new Date().getFullYear();

  const socialLinks = [
    {
      href: "https://github.com/darshanbajgain",
      label: "GitHub",
      icon: Github,
    },
    {
      href: "https://x.com/darshan_bajgain",
      label: "Twitter",
      icon: Twitter,
    },
    {
      href: "mailto:<EMAIL>",
      label: "Email",
      icon: Mail,
    },
  ];

  const footerLinks = [
    { href: "#features", label: "Features" },
    { href: "#themes", label: "Themes" },
    { href: "#pricing", label: "Pricing" },
    { href: "/changelog", label: "What's New" },
  ];

  return (
    <footer className="bg-backgroundSecondary border-t border-borderPrimary">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          <div className="flex items-center gap-2">
            <Image src="/icon.png" alt="logo" width={28} height={28} />
            <span className="font-bold text-lg gradient-text">Profolify</span>
          </div>
          <div className="flex items-center gap-6 text-sm">
            {footerLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-textSecondary hover:text-brandPrimary transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </div>
          <div className="flex items-center gap-4">
            {socialLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-textSecondary hover:text-brandPrimary transition-colors"
                target="_blank"
                rel="noopener noreferrer"
                aria-label={link.label}
              >
                <link.icon className="w-5 h-5" />
              </Link>
            ))}
          </div>
        </div>
        <div className="mt-8 pt-6 border-t border-borderPrimary/50 text-center text-sm text-textSecondary">
          <p>&copy; {currentYear} Profolify. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}
