"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Star, ArrowRight } from "lucide-react";
import { useRouter } from "next/navigation";

const currentFeatures = [
  "Unlimited portfolios",
  "2 beautiful themes",
  "Intuitive WYSIWYG editing",
  "Live site publishing",
  "Static HTML export",
  "SEO-friendly URLs",
  "Fully mobile responsive",
  "No watermarks or branding",
];

export function PricingSection() {
  const router = useRouter();

  return (
    <section
      id="pricing"
      className="py-24 lg:py-32 bg-backgroundPrimary relative overflow-hidden"
    >
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundSecondary via-backgroundPrimary to-backgroundSecondary"></div>
      <div className="absolute top-0 right-0 w-1/2 h-full bg-gradient-to-l from-brandPrimary/5 to-transparent blur-3xl"></div>
      <div className="absolute bottom-0 left-0 w-1/2 h-full bg-gradient-to-r from-brandAccent/5 to-transparent blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-brandAccent/10 border border-brandAccent/20 mb-8">
            <Star className="w-4 h-4 text-brandAccent" />
            <span className="text-sm font-semibold text-brandAccent">
              Free Forever
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="gradient-text">Start Building Today,</span>
            <br />
            <span className="text-textPrimary">No Payment Required</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Get all the essential features to create a professional portfolio.
            No credit card, no time limits, no hidden fees.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-gradient-to-br from-brandPrimary/5 to-brandSecondary/5 rounded-2xl p-8 border border-brandPrimary/20 shadow-2xl shadow-brandPrimary/10">
            <div className="text-center mb-8">
              <h3 className="text-3xl font-bold text-textPrimary mb-2">Free Plan</h3>
              <p className="text-textSecondary">All features are free while we&apos;re in early access.</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
              {currentFeatures.map((feature, index) => (
                <div key={index} className="flex items-center gap-3">
                  <div className="w-5 h-5 rounded-full bg-brandAccent flex items-center justify-center">
                    <Check className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-textSecondary">{feature}</span>
                </div>
              ))}
            </div>

            <Button
              variant="cta"
              size="lg"
              onClick={() => router.push("/login")}
              className="w-full py-4 group"
            >
              <span className="flex items-center justify-center gap-2">
                Start Building Free
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
              </span>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
