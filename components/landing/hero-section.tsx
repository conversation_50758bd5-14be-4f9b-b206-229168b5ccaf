"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>rk<PERSON>,
  Download,
  Edit3,
  <PERSON>ap,
  Figma,
  Code,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { Button } from "../ui/button";
import Image from "next/image";
import { motion } from "framer-motion";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
    },
  },
};

const floatingItemVariants = {
  hidden: { opacity: 0, scale: 0.5 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.5,
    },
  },
};



export function HeroSection() {
  const router = useRouter();

  return (
    <section
      id="home"
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-backgroundPrimary pt-24 lg:pt-32 pb-16"
    >
      {/* Enhanced background elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <motion.div
          className="absolute -top-20 -left-20 w-96 h-96 bg-brandPrimary/5 rounded-full blur-3xl"
          animate={{
            y: [0, -20, 0],
            x: [0, 10, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute -bottom-20 -right-20 w-96 h-96 bg-brandAccent/5 rounded-full blur-3xl"
          animate={{
            y: [0, 20, 0],
            x: [0, -10, 0],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2,
          }}
        />
        <div
          className="absolute inset-0 opacity-[0.02] bg-grid-pattern"
          style={{ animation: "gridFloat 20s linear infinite" }}
        ></div>
      </div>

      <motion.div
        className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column: Text Content */}
          <motion.div
            className="text-center lg:text-left"
            variants={containerVariants}
          >
            <motion.div
              className="inline-flex items-center space-x-2 bg-brandAccent/10 rounded-full px-4 py-2 mb-8 border border-brandAccent/20"
              variants={itemVariants}
            >
              <Sparkles className="w-4 h-4 text-brandAccent" />
              <span className="text-sm font-medium text-brandAccent">
                Free Portfolio Builder • No Code Required
              </span>
            </motion.div>

            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight mb-6"
              variants={itemVariants}
            >
              <span className="gradient-text">Build & Export</span>
              <br />
              <span className="text-textPrimary">
                Stunning Portfolios
              </span>
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-textSecondary max-w-xl mx-auto lg:mx-0 mb-12 leading-relaxed"
              variants={itemVariants}
            >
              Create professional portfolios with our intuitive editor. Export as static HTML,
              choose from beautiful themes, and showcase your work with SEO-optimized designs.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-4 mb-12"
              variants={itemVariants}
            >
              <Button
                variant="cta"
                size="lg"
                onClick={() => router.push("/login")}
                className="px-8 py-4 w-full sm:w-auto"
              >
                <span className="flex items-center gap-2">
                  Start Building Free
                  <ArrowRight className="h-5 w-5" />
                </span>
              </Button>
              <Button
                variant="outline"
                size="lg"
                onClick={() => document.getElementById("features")?.scrollIntoView({ behavior: "smooth" })}
                className="px-8 py-4 w-full sm:w-auto"
              >
                See Features
              </Button>
            </motion.div>

            <motion.div
              className="flex items-center justify-center lg:justify-start gap-4 text-textSecondary"
              variants={itemVariants}
            >
              <div className="flex items-center gap-2">
                <Edit3 className="w-5 h-5 text-brandPrimary" />
                <span>No Code Editor</span>
              </div>
              <div className="flex items-center gap-2">
                <Download className="w-5 h-5 text-brandAccent" />
                <span>Static HTML Export</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Column: Visual Element */}
          <motion.div
            className="relative hidden lg:block"
            variants={itemVariants}
          >
            <div className="relative w-full mx-auto rounded-3xl">
              {/* Placeholder for a visual, e.g., an image or animation */}
              <motion.div
                className="absolute inset-0 flex items-center justify-center"
                variants={itemVariants}
              >
                <Image
                  src="/thumbnails/Creative Minimalist.png" // Replace with your desired image
                  alt="Portfolio Builder Visual"
                  width={1000}
                  height={1000}
                  className="opacity-80 rounded-xl shadow-xl drop-shadow-brandPrimary border border-brandPrimary/20"
                />
              </motion.div>

              {/* Floating UI elements */}
              <motion.div
                className="absolute flex flex-col items-center bottom-30 -left-20 w-24 h-24 bg-backgroundPrimary p-3 rounded-2xl border border-borderPrimary shadow-lg"
                variants={floatingItemVariants}
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 3, repeat: Infinity, ease: "easeInOut" }}
              >
                <Figma className="w-8 h-8 text-brandSecondary" />
                <p className="text-xs font-semibold mt-1 text-center">Design Ready</p>
              </motion.div>
              <motion.div
                className="absolute flex flex-col items-center bottom-35 right-0 w-24 h-24 bg-backgroundPrimary p-3 rounded-2xl border border-borderPrimary shadow-lg"
                variants={floatingItemVariants}
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              >
                <Code className="w-8 h-8 text-brandAccent" />
                <p className="text-xs font-semibold mt-1 text-center">Dev Friendly</p>
              </motion.div>
              <motion.div
                className="absolute flex flex-col items-center top-20 -left-10 w-20 h-20 bg-backgroundPrimary p-3 rounded-2xl border border-borderPrimary shadow-lg"
                variants={floatingItemVariants}
                animate={{ y: [0, 15, 0] }}
                transition={{ duration: 5, repeat: Infinity, ease: "easeInOut" }}
              >
                <Zap className="w-6 h-6 text-brandPrimary" />
                <p className="text-xs font-semibold mt-1 text-center">SEO Optimized</p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </section>
  );
}
