"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthenticationContext";
import { useState, useEffect } from "react";
import { Menu, X } from "lucide-react";
import Image from "next/image";

export function LandingNavbar() {
  const router = useRouter();
  const { user } = useAuth();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20); 
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navLinks = [
    { href: "#home", label: "Home" },
    { href: "#features", label: "Features" },
    { href: "#themes", label: "Themes" },
    { href: "#pricing", label: "Pricing" },
  ];

  return (
    <header
      className={`fixed top-0 w-full right-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-backgroundPrimary/80 border-b border-borderPrimary/30 shadow-lg backdrop-blur-xl"
          : "bg-transparent"
      }`}
    >
      <nav className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          <Link href="/" className="flex items-center gap-2">
            <Image src="/icon.png" alt="logo" width={32} height={32} />
            <span className="font-bold text-xl lg:text-2xl gradient-text">
              Profolify
            </span>
          </Link>

          <div className="hidden md:flex items-center space-x-1">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="px-4 py-2 text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium rounded-lg hover:bg-brandPrimary/5"
              >
                {link.label}
              </Link>
            ))}
          </div>

          <div className="flex items-center space-x-3">
            <div className="hidden sm:flex">
              <Button
                variant="default"
                onClick={() => router.push(user ? "/dashboard" : "/login")}
              >
                {user ? "Go to Dashboard" : "Get Started"}
              </Button>
            </div>

            <Button
              variant="ghost"
              size="icon"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? <X /> : <Menu />}
            </Button>
          </div>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden bg-backgroundPrimary/90 backdrop-blur-lg rounded-lg p-4 mt-2">
            <div className="flex flex-col space-y-2">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className="block text-textSecondary hover:text-brandPrimary transition-colors duration-200 font-medium py-2 px-3 rounded-md hover:bg-brandPrimary/5"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}
              <Button
                variant="default"
                onClick={() => {
                  router.push(user ? "/dashboard" : "/login");
                  setIsMobileMenuOpen(false);
                }}
                className="w-full mt-2"
              >
                {user ? "Go to Dashboard" : "Get Started"}
              </Button>
            </div>
          </div>
        )}
      </nav>
    </header>
  );
}
