"use client";

import { Smartphone, Search, Download, Zap, ArrowRight } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useRouter } from "next/navigation";
import { AnimatedBorderCard } from "../ui/animated-border-card";

const seoContent = [
  {
    icon: Download,
    title: "Export Portfolio as Static HTML",
    description: "Download your complete portfolio as static HTML, CSS, and JavaScript files. Host anywhere, no dependencies required.",
    keywords: ["static HTML export", "portfolio HTML download", "static website generator"]
  },
  {
    icon: Search,
    title: "SEO-Optimized Portfolio Websites",
    description: "All portfolios are automatically optimized for search engines with proper meta tags, structured data, and clean URLs.",
    keywords: ["SEO portfolio", "search optimized portfolio", "discoverable portfolio"]
  },
  {
    icon: Smartphone,
    title: "Mobile-Responsive Portfolio Themes",
    description: "Every portfolio theme is fully responsive and mobile-optimized, ensuring a perfect look on all devices.",
    keywords: ["mobile portfolio", "responsive portfolio", "mobile-friendly portfolio"]
  },
  {
    icon: Zap,
    title: "Quick Portfolio Creation in Minutes",
    description: "Build and publish professional portfolios in minutes with our intuitive editor and pre-designed themes.",
    keywords: ["quick portfolio", "fast portfolio builder", "instant portfolio"]
  }
];

export function SEOContentSection() {
  const router = useRouter();

  return (
    <section className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundPrimary via-backgroundSecondary to-backgroundPrimary"></div>
      <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-brandAccent/5 to-transparent blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-1/2 h-full bg-gradient-to-l from-brandSecondary/5 to-transparent blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-textPrimary">Built for</span>
            <span className="gradient-text"> Every Professional</span>
          </h2>
          <p className="text-xl md:text-2xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Whether you&apos;re a developer, designer, or freelancer, our portfolio builder 
            has everything you need to create a stunning online presence.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto mb-16">
          {seoContent.map((item, index) => (
            <AnimatedBorderCard key={index}>
              <div className="p-8">
                <div className="flex items-start gap-6">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-brandPrimary to-brandSecondary flex items-center justify-center flex-shrink-0 shadow-lg shadow-brandPrimary/20">
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-textPrimary mb-3">
                      {item.title}
                    </h3>
                    <p className="text-textSecondary leading-relaxed mb-4">
                      {item.description}
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {item.keywords.map((keyword, keyIndex) => (
                        <span
                          key={keyIndex}
                          className="px-3 py-1 text-xs font-medium bg-brandPrimary/10 text-brandPrimary rounded-full border border-brandPrimary/20"
                        >
                          {keyword}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedBorderCard>
          ))}
        </div>

        <div className="text-center bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 rounded-2xl p-12 border border-brandPrimary/20 shadow-xl shadow-brandPrimary/10">
          <h3 className="text-3xl md:text-4xl font-bold mb-4">
            <span className="gradient-text">Ready to Build Your Portfolio?</span>
          </h3>
          <p className="text-lg text-textSecondary max-w-2xl mx-auto mb-8">
            Join thousands of professionals who have already created stunning portfolios with our free builder.
          </p>
          <Button
            variant="cta"
            size="lg"
            onClick={() => router.push("/login")}
            className="px-8 py-4 group"
          >
            <span className="flex items-center gap-2">
              Start Building Free
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </span>
          </Button>
        </div>
      </div>
    </section>
  );
}
