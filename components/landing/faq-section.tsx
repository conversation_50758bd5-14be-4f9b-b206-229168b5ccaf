"use client";

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { HelpCircle, MessageCircle, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";

const faqs = [
  {
    question: "Is this portfolio builder really free?",
    answer:
      "Yes! Our portfolio builder is completely free. Create professional portfolios, export static HTML files, and publish online without any hidden costs or time limits.",
  },
  {
    question: "How do I export my portfolio as static HTML?",
    answer:
      "Simply click the &apos;Export&apos; button in your portfolio editor to download a ZIP file containing your portfolio as static HTML, CSS, and JavaScript files.",
  },
  {
    question: "What makes this different from other portfolio builders?",
    answer:
      "Our builder features revolutionary Live DOM Capture for pixel-perfect exports, a true WYSIWYG editor, and SEO-optimized themes—all completely free.",
  },
  {
    question: "Do I need coding skills to build a portfolio?",
    answer:
      "No coding required! Our portfolio builder features an intuitive WYSIWYG editor. Simply click to edit text, upload images, and customize your portfolio.",
  },
  {
    question: "Are the portfolio themes mobile-responsive?",
    answer:
      "Yes! All our themes are fully mobile-responsive and optimized for all devices, ensuring your portfolio looks perfect on any screen size.",
  },
  {
    question: "Can I use this for my resume or CV?",
    answer:
      "Absolutely! Our portfolio builder is perfect for creating resume portfolios and CV websites to showcase your work experience, skills, and projects.",
  },
];

export function FAQSection() {
  return (
    <section className="py-24 lg:py-32 bg-backgroundSecondary relative overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-backgroundPrimary via-backgroundSecondary to-backgroundPrimary"></div>
      <div className="absolute top-0 left-0 w-1/2 h-full bg-gradient-to-r from-brandAccent/5 to-transparent blur-3xl"></div>
      <div className="absolute bottom-0 right-0 w-1/2 h-full bg-gradient-to-l from-brandSecondary/5 to-transparent blur-3xl"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-3 px-4 py-2 rounded-full bg-brandPrimary/10 border border-brandPrimary/20 mb-8">
            <HelpCircle className="w-5 h-5 text-brandPrimary" />
            <span className="text-sm font-semibold text-brandPrimary">
              Frequently Asked Questions
            </span>
          </div>

          <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
            <span className="text-textPrimary">Got Questions?</span>
            <br />
            <span className="gradient-text">We Have Answers</span>
          </h2>

          <p className="text-lg md:text-xl text-textSecondary max-w-3xl mx-auto leading-relaxed">
            Everything you need to know about Profolify, from getting started to
            advanced features.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-backgroundPrimary/50 rounded-2xl p-8 border border-borderPrimary mb-12">
            <Accordion type="single" collapsible className="space-y-4">
              {faqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`item-${index}`}
                  className="border border-borderPrimary/50 rounded-xl bg-backgroundSecondary/50 hover:border-brandPrimary/30 transition-colors duration-300"
                >
                  <AccordionTrigger className="text-left text-lg font-semibold text-textPrimary hover:text-brandPrimary transition-colors py-4 px-6 [&[data-state=open]]:text-brandPrimary">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-textSecondary leading-relaxed pb-4 px-6 pt-0">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>

          <div className="text-center bg-gradient-to-r from-brandPrimary/10 to-brandSecondary/10 rounded-2xl p-12 border border-brandPrimary/20 shadow-xl shadow-brandPrimary/10">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-r from-brandPrimary to-brandSecondary flex items-center justify-center shadow-lg shadow-brandPrimary/25">
                <MessageCircle className="w-8 h-8 text-white" />
              </div>
            </div>

            <h3 className="text-3xl font-bold text-textPrimary mb-4">
              Still Have Questions?
            </h3>

            <p className="text-textSecondary mb-8 max-w-2xl mx-auto">
              Can&apos;t find the answer you&apos;re looking for? Our friendly support
              team is here to help you get the most out of Profolify.
            </p>

            <Link href="mailto:<EMAIL>">
              <Button variant="cta" size="lg" className="group">
                <span className="flex items-center gap-2">
                  Contact Support
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
                </span>
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
}
