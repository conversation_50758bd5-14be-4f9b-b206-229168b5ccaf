"use client";

import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export function AnimatedBorderCard({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn("relative w-full overflow-hidden rounded-xl p-px", className)}>
      <motion.div
        initial={{
          background:
            "conic-gradient(from 0deg, #7C3AED00, #7C3AED, #7C3AED00 50%)",
        }}
        animate={{
          background:
            "conic-gradient(from 360deg, #7C3AED00, #7C3AED, #7C3AED00 50%)",
        }}
        transition={{
          duration: 25,
          repeat: Infinity,
          ease: "linear",
        }}
        className="absolute inset-0 z-0"
      />
      <div className="relative z-10 h-full w-full rounded-xl bg-backgroundSecondary">
        {children}
      </div>
    </div>
  );
}
